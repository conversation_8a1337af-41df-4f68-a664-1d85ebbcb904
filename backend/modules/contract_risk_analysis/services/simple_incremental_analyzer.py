"""
简单增量分析器 - 最小改动实现方案
使用现有架构，通过简单的数据合并和去重逻辑实现增量功能
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class SimpleIncrementalAnalyzer:
    """简单增量分析器 - 最小改动版本"""
    
    def __init__(self, task_id: str = None):
        self.task_id = task_id
        self.logger = logging.getLogger(__name__)
        
    def process_incremental_data(self, df: pd.DataFrame, processing_mode: str = 'incremental', 
                               parent_task_id: str = None) -> Dict[str, Any]:
        """
        处理增量数据 - 简化版本
        
        Args:
            df: 新的交易数据
            processing_mode: 处理模式 ('incremental' 或 'rerun')
            parent_task_id: 父任务ID（增量模式时使用）
            
        Returns:
            处理结果字典
        """
        if df.empty:
            logger.warning("输入数据为空，跳过增量分析")
            return self._build_empty_result(processing_mode)
        
        logger.info(f"开始{processing_mode}模式数据处理，数据量: {len(df)}")
        start_time = datetime.now()
        
        try:
            # 1. 使用现有分析器进行风险检测
            from .contract_analyzer import CTContractAnalyzer
            analyzer = CTContractAnalyzer(task_id=self.task_id)
            
            # 执行标准分析
            analysis_result = analyzer.process_contract_data(df)
            
            if isinstance(analysis_result, dict):
                # 新版本返回格式
                new_results = analysis_result.get('results', [])
                complete_positions = analysis_result.get('complete_positions', {})
                statistics = analysis_result.get('statistics', {})
            else:
                # 旧版本返回格式
                new_results = analysis_result
                complete_positions = {}
                statistics = {}
            
            # 2. 根据处理模式进行数据合并
            if processing_mode == 'incremental' and parent_task_id:
                # 增量模式：合并历史数据
                final_results = self._merge_with_historical_data(new_results, parent_task_id)
                logger.info(f"增量合并完成，新增: {len(new_results)}, 总计: {len(final_results)}")
            else:
                # 重跑模式或首次分析：直接使用新结果
                final_results = new_results
                logger.info(f"使用新结果，总计: {len(final_results)}")
            
            # 3. 保存结果（使用现有存储逻辑）
            self._save_results(final_results, processing_mode, parent_task_id)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'status': 'success',
                'processing_mode': processing_mode,
                'task_id': self.task_id,
                'parent_task_id': parent_task_id,
                'new_risks_found': len(new_results),
                'total_risks': len(final_results),
                'processing_time': processing_time,
                'statistics': statistics,
                'message': f'{processing_mode}模式处理完成'
            }
            
        except Exception as e:
            logger.error(f"增量分析失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'status': 'error',
                'processing_mode': processing_mode,
                'task_id': self.task_id,
                'error': str(e),
                'message': f'{processing_mode}模式处理失败'
            }
    
    def _merge_with_historical_data(self, new_results: List[Dict], parent_task_id: str) -> List[Dict]:
        """合并历史数据 - 简化版本"""
        try:
            # 获取历史数据
            historical_results = self._get_historical_results(parent_task_id)
            
            if not historical_results:
                logger.info("未找到历史数据，直接使用新结果")
                return new_results
            
            # 简单去重合并：基于 member_id + contract_name + detection_type
            merged_results = []
            seen_keys = set()
            
            # 1. 先添加新数据（优先级更高）
            for result in new_results:
                key = self._generate_dedup_key(result)
                if key not in seen_keys:
                    merged_results.append(result)
                    seen_keys.add(key)
            
            # 2. 再添加不重复的历史数据
            for result in historical_results:
                key = self._generate_dedup_key(result)
                if key not in seen_keys:
                    merged_results.append(result)
                    seen_keys.add(key)
            
            logger.info(f"数据合并完成 - 新数据: {len(new_results)}, 历史数据: {len(historical_results)}, 合并后: {len(merged_results)}")
            return merged_results
            
        except Exception as e:
            logger.error(f"合并历史数据失败: {e}")
            # 失败时返回新数据
            return new_results
    
    def _generate_dedup_key(self, result: Dict) -> str:
        """生成去重键"""
        member_id = result.get('member_id', result.get('user_id', ''))
        contract_name = result.get('contract_name', '')
        detection_type = result.get('detection_type', result.get('algorithm_type', ''))
        
        # 处理对手方信息
        counterparty = ''
        if 'counterparty_id' in result:
            counterparty = str(result['counterparty_id'])
        elif 'counterparty_ids' in result and result['counterparty_ids']:
            counterparty = str(result['counterparty_ids'][0])
        
        return f"{member_id}|{contract_name}|{detection_type}|{counterparty}"
    
    def _get_historical_results(self, parent_task_id: str) -> List[Dict]:
        """获取历史分析结果"""
        try:
            from database.repositories.contract_risk_repository import ContractRiskRepository
            repository = ContractRiskRepository()
            
            # 获取父任务的分析结果
            historical_data = repository.get_analysis_result(parent_task_id)
            
            if not historical_data or 'result_data' not in historical_data:
                return []
            
            result_data = historical_data['result_data']
            if isinstance(result_data, str):
                result_data = json.loads(result_data)
            
            # 提取风险结果
            if 'suspicious' in result_data:
                return result_data['suspicious']
            elif 'contract_risks' in result_data:
                return result_data['contract_risks']
            else:
                return []
                
        except Exception as e:
            logger.error(f"获取历史结果失败: {e}")
            return []
    
    def _save_results(self, results: List[Dict], processing_mode: str, parent_task_id: str = None):
        """保存分析结果"""
        try:
            from database.repositories.contract_risk_repository import ContractRiskRepository
            from database.repositories.task_repository import TaskRepository
            
            # 构建结果数据
            result_data = {
                'suspicious': results,
                'contract_risks': results,  # 兼容性
                'summary': {
                    'total_risks': len(results),
                    'processing_mode': processing_mode,
                    'parent_task_id': parent_task_id,
                    'analysis_time': datetime.now().isoformat()
                }
            }
            
            # 保存到contract_risk_analysis表
            repository = ContractRiskRepository()
            repository.save_analysis_result(
                task_id=self.task_id,
                analysis_type='suspected_wash_trading',
                filename='incremental_analysis',
                total_contracts=len(results),
                risk_contracts=len(results),
                wash_trading_count=len([r for r in results if 'wash_trading' in r.get('detection_type', '').lower()]),
                cross_bd_count=0,
                result_data=result_data
            )
            
            # 更新任务状态
            task_repository = TaskRepository()
            task_repository.update_task_status(
                task_id=self.task_id,
                status='completed',
                progress=100,
                message=f'{processing_mode}模式分析完成，发现 {len(results)} 个风险点'
            )
            
            logger.info(f"结果保存成功: {self.task_id}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise
    
    def _build_empty_result(self, processing_mode: str) -> Dict[str, Any]:
        """构建空结果"""
        return {
            'status': 'success',
            'processing_mode': processing_mode,
            'task_id': self.task_id,
            'new_risks_found': 0,
            'total_risks': 0,
            'processing_time': 0,
            'message': '输入数据为空，无需处理'
        }
